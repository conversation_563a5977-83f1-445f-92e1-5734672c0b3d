import React from 'react';

// Example component that can be dynamically set in the header
const ExampleHeaderComponent = () => {
    return (
        <div style={{
            padding: '15px',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            border: '1px solid rgba(0, 123, 255, 0.3)',
            borderRadius: '8px',
            color: 'white',
            textAlign: 'center',
            margin: '10px 0'
        }}>
            <h3 style={{ margin: '0 0 10px 0', color: '#007bff' }}>
                🎉 Dynamic Header Component
            </h3>
            <p style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
                This component was dynamically set using the setDynamicHeaderComponent function!
            </p>
            <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
                <button 
                    style={{
                        padding: '8px 16px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer'
                    }}
                    onClick={() => {
                        // Clear the header component
                        (window as any).setOrderManagementHeaderComponent?.(null);
                    }}
                >
                    Clear Header
                </button>
                <button 
                    style={{
                        padding: '8px 16px',
                        backgroundColor: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer'
                    }}
                    onClick={() => {
                        // Set a different component
                        const newComponent = (
                            <div style={{
                                padding: '10px',
                                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                                border: '1px solid rgba(40, 167, 69, 0.3)',
                                borderRadius: '4px',
                                color: '#28a745',
                                textAlign: 'center'
                            }}>
                                <strong>✅ Header Updated!</strong>
                                <p style={{ margin: '5px 0 0 0', fontSize: '12px' }}>
                                    You can dynamically change the header content anytime.
                                </p>
                            </div>
                        );
                        (window as any).setOrderManagementHeaderComponent?.(newComponent);
                    }}
                >
                    Update Header
                </button>
            </div>
        </div>
    );
};

export default ExampleHeaderComponent;
