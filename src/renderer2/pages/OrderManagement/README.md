# OrderManagement Dynamic Header Component

This document explains how to use the dynamic header component feature in the OrderManagement component.

## Overview

The OrderManagement component now includes a `Header` component that can dynamically display any React component. This allows you to set custom content in the header area programmatically.

## How It Works

1. **Header Component**: A simple wrapper component that renders any children passed to it
2. **Setter Function**: `setDynamicHeaderComponent` function that updates the header content
3. **Global Access**: The setter function is exposed globally via `window.setOrderManagementHeaderComponent`

## Usage

### Method 1: Using the Global Window Function

```typescript
// Set a component in the header
(window as any).setOrderManagementHeaderComponent(
  <div style={{ padding: '10px', color: 'white' }}>
    <h3>Custom Header</h3>
    <p>This is dynamically set content!</p>
  </div>
);

// Clear the header
(window as any).setOrderManagementHeaderComponent(null);
```

### Method 2: Using the Utility Functions

```typescript
import { setOrderManagementHeader, clearOrderManagementHeader } from './utils/headerUtils';

// Set a component
setOrderManagementHeader(
  <div style={{ padding: '10px', color: 'white' }}>
    <h3>Custom Header</h3>
    <p>This is dynamically set content!</p>
  </div>
);

// Clear the header
clearOrderManagementHeader();
```

### Method 3: Using the Example Component

```typescript
import { setOrderManagementHeader } from './utils/headerUtils';
import ExampleHeaderComponent from './components/ExampleHeaderComponent';

// Set the example component
setOrderManagementHeader(<ExampleHeaderComponent />);
```

## Example Components

### Simple Text Header
```typescript
const simpleHeader = (
  <div style={{
    padding: '15px',
    backgroundColor: 'rgba(0, 123, 255, 0.1)',
    border: '1px solid rgba(0, 123, 255, 0.3)',
    borderRadius: '8px',
    color: 'white',
    textAlign: 'center'
  }}>
    <h3>Order Status: Processing</h3>
    <p>Your order is being processed...</p>
  </div>
);

setOrderManagementHeader(simpleHeader);
```

### Interactive Header with Buttons
```typescript
const interactiveHeader = (
  <div style={{ padding: '10px', color: 'white' }}>
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <h3>Order Actions</h3>
      <div>
        <button onClick={() => console.log('Export clicked')}>Export</button>
        <button onClick={() => console.log('Print clicked')}>Print</button>
      </div>
    </div>
  </div>
);

setOrderManagementHeader(interactiveHeader);
```

## Styling

The header container has the CSS class `dynamicHeaderContainer` with the following default styles:

```scss
.dynamicHeaderContainer {
  width: 100%;
  padding: 10px;
  background-color: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 10px;
  
  > * {
    width: 100%;
  }
}
```

You can override these styles or add your own styling to the components you pass to the header.

## Best Practices

1. **Check Availability**: Use `isOrderManagementHeaderAvailable()` to check if the setter function is available before using it
2. **Clean Up**: Clear the header when navigating away or when the component is no longer needed
3. **Responsive Design**: Ensure your header components work well on different screen sizes
4. **Consistent Styling**: Follow the existing design patterns and color scheme of the application

## Files Modified/Created

- `OrderManagement.tsx` - Added Header component and setter function
- `OrderManagement.module.scss` - Added styles for the dynamic header container
- `components/ExampleHeaderComponent.tsx` - Example component demonstrating usage
- `utils/headerUtils.ts` - Utility functions for easier header management
- `README.md` - This documentation file

## Notes

- The setter function is only available when the OrderManagement component is mounted
- The header content will persist until explicitly changed or cleared
- Multiple components can use the setter function, but only one header component can be displayed at a time
- The header is positioned above the existing button container in the OrderManagement layout
