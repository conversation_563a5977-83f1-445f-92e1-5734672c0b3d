import React from 'react';

/**
 * Utility functions for managing the dynamic header component in OrderManagement
 */

/**
 * Sets a component to be displayed in the OrderManagement header
 * @param component - React component or JSX element to display in the header
 */
export const setOrderManagementHeader = (component: React.ReactNode) => {
    if (typeof window !== 'undefined' && (window as any).setOrderManagementHeaderComponent) {
        (window as any).setOrderManagementHeaderComponent(component);
    } else {
        console.warn('OrderManagement header setter function is not available. Make sure the OrderManagement component is mounted.');
    }
};

/**
 * Clears the header component
 */
export const clearOrderManagementHeader = () => {
    setOrderManagementHeader(null);
};

/**
 * Checks if the header setter function is available
 * @returns boolean indicating if the setter function is available
 */
export const isOrderManagementHeaderAvailable = (): boolean => {
    return typeof window !== 'undefined' && 
           typeof (window as any).setOrderManagementHeaderComponent === 'function';
};

/**
 * Example usage:
 * 
 * import { setOrderManagementHeader, clearOrderManagementHeader } from './utils/headerUtils';
 * import ExampleHeaderComponent from './components/ExampleHeaderComponent';
 * 
 * // Set a component
 * setOrderManagementHeader(<ExampleHeaderComponent />);
 * 
 * // Set custom JSX
 * setOrderManagementHeader(
 *   <div style={{ padding: '10px', color: 'white' }}>
 *     <h3>Custom Header</h3>
 *     <p>This is a custom header component</p>
 *   </div>
 * );
 * 
 * // Clear the header
 * clearOrderManagementHeader();
 */
